import { useState, useCallback, useEffect } from 'react';
import { useChatbotAPI } from './useChatbotAPI';
import { useWebhookAPI } from './useWebhookAPI';
import type { Mensagem, ConfiguracaoAPI, RespostaEnviarMensagem } from './useChatbotAPI';

export type TipoConexao = 'api' | 'webhook';

export interface ConfiguracaoUnificada extends ConfiguracaoAPI {
  tipoConexao: TipoConexao;
  webhookUrl: string;
}

export const useChatbotUnificado = (configuracao: ConfiguracaoUnificada) => {
  const [tipoAtivo, setTipoAtivo] = useState<TipoConexao>(configuracao.tipoConexao);

  // Atualizar tipoAtivo quando a configuração mudar
  useEffect(() => {
    setTipoAtivo(configuracao.tipoConexao);
  }, [configuracao.tipoConexao]);

  // Hooks para ambos os tipos de conexão
  const apiLocal = useChatbotAPI(configuracao.apiUrl);
  const webhookN8N = useWebhookAPI(configuracao.webhookUrl);

  // Selecionar o hook ativo baseado na configuração
  const hookAtivo = tipoAtivo === 'api' ? apiLocal : webhookN8N;

  const alternarTipoConexao = useCallback((novoTipo: TipoConexao) => {
    setTipoAtivo(novoTipo);
  }, []);

  const enviarMensagem = useCallback(async (
    mensagem: string,
    sessaoId: string | null = null,
    usuarioId: string | null = null,
    contextoAdicional: Record<string, any> = {}
  ): Promise<RespostaEnviarMensagem> => {
    const contextoComTipo = {
      ...contextoAdicional,
      tipo_conexao: tipoAtivo,
      fonte: tipoAtivo === 'api' ? 'plasmo_extension_api' : 'plasmo_extension_webhook'
    };

    if (tipoAtivo === 'api') {
      return apiLocal.enviarMensagem(mensagem, sessaoId, usuarioId, contextoComTipo);
    } else {
      return webhookN8N.enviarMensagem(mensagem, sessaoId, usuarioId, contextoComTipo);
    }
  }, [tipoAtivo, apiLocal, webhookN8N]);

  const testarConexao = useCallback(async (): Promise<boolean> => {
    try {
      if (tipoAtivo === 'api') {
        // Para API local, testar endpoint de health
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), configuracao.timeout);

        const response = await fetch(`${configuracao.apiUrl}/health`, {
          method: 'GET',
          signal: controller.signal,
        });

        clearTimeout(timeoutId);
        return response.ok;
      } else {
        // Para webhook, usar o método de teste específico
        return await webhookN8N.testarWebhook();
      }
    } catch (error) {
      console.error('Erro ao testar conexão:', error);
      return false;
    }
  }, [tipoAtivo, configuracao.apiUrl, configuracao.timeout, webhookN8N]);

  const obterHistorico = useCallback(async (
    sessaoId: string,
    limite: number = 50
  ): Promise<Mensagem[]> => {
    if (tipoAtivo === 'api') {
      return apiLocal.obterHistorico(sessaoId, limite);
    } else {
      // Para webhook, retornar array vazio ou implementar lógica específica
      console.warn('Histórico não disponível para conexão via webhook');
      return [];
    }
  }, [tipoAtivo, apiLocal]);

  const listarSessoes = useCallback(async (
    usuarioId: string | null = null,
    limite: number = 20
  ) => {
    if (tipoAtivo === 'api') {
      return apiLocal.listarSessoes(usuarioId, limite);
    } else {
      // Para webhook, retornar array vazio ou implementar lógica específica
      console.warn('Listagem de sessões não disponível para conexão via webhook');
      return [];
    }
  }, [tipoAtivo, apiLocal]);

  const finalizarSessao = useCallback(async (sessaoId: string): Promise<void> => {
    if (tipoAtivo === 'api') {
      return apiLocal.finalizarSessao(sessaoId);
    } else {
      // Para webhook, apenas log (N8N pode não ter endpoint específico)
      console.log('Sessão finalizada (webhook):', sessaoId);
    }
  }, [tipoAtivo, apiLocal]);

  const limparErro = useCallback(() => {
    apiLocal.limparErro();
    webhookN8N.limparErro();
  }, [apiLocal, webhookN8N]);

  return {
    // Estados
    carregando: hookAtivo.carregando,
    erro: hookAtivo.erro,
    tipoConexaoAtivo: tipoAtivo,

    // Métodos da API
    enviarMensagem,
    obterHistorico,
    listarSessoes,
    finalizarSessao,
    testarConexao,

    // Controle de tipo de conexão
    alternarTipoConexao,

    // Utilitários
    limparErro,

    // Informações sobre disponibilidade de recursos
    recursos: {
      historico: tipoAtivo === 'api',
      listarSessoes: tipoAtivo === 'api',
      finalizarSessao: tipoAtivo === 'api',
    }
  };
};
