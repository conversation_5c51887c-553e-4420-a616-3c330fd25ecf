import React, { useState, useEffect } from 'react';
import { ArrowLeft, Eye, StopCircle, RefreshCw, X, Download } from 'lucide-react';
import { useChatbotUnificado } from '../hooks/useChatbotUnificado';
import type { SessaoChat, Mensagem } from '../hooks/useChatbotAPI';
import type { ConfiguracaoUnificada } from '../hooks/useChatbotUnificado';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Badge } from '@/src/components/ui/badge';

interface HistoricoSessoesProps {
  onClose: () => void;
  onCarregarSessao: (sessaoId: string, mensagens: Mensagem[]) => void;
  configuracao: ConfiguracaoUnificada;
}

const HistoricoSessoes: React.FC<HistoricoSessoesProps> = ({
  onClose,
  onCarregarSessao,
  configuracao
}) => {
  const [sessoes, setSessoes] = useState<SessaoChat[]>([]);
  const [sessaoSelecionada, setSessaoSelecionada] = useState<string | null>(null);
  const [mensagensSessao, setMensagensSessao] = useState<Mensagem[]>([]);
  const [visualizandoDetalhes, setVisualizandoDetalhes] = useState(false);

  const {
    listarSessoes,
    obterHistorico,
    finalizarSessao,
    carregando,
    erro,
    recursos
  } = useChatbotUnificado(configuracao);

  useEffect(() => {
    // Só carregar sessões se o recurso estiver disponível
    if (recursos.listarSessoes) {
      carregarSessoes();
    }
  }, [recursos.listarSessoes]);

  const carregarSessoes = async () => {
    try {
      const sessoesCarregadas = await listarSessoes(configuracao.usuarioId, 50);
      setSessoes(sessoesCarregadas);
    } catch (error) {
      console.error('Erro ao carregar sessões:', error);
    }
  };

  const visualizarDetalhes = async (sessaoId: string) => {
    try {
      const mensagens = await obterHistorico(sessaoId);
      setMensagensSessao(mensagens);
      setSessaoSelecionada(sessaoId);
      setVisualizandoDetalhes(true);
    } catch (error) {
      console.error('Erro ao carregar detalhes da sessão:', error);
    }
  };

  const carregarSessaoNoChat = () => {
    if (sessaoSelecionada && mensagensSessao.length > 0) {
      onCarregarSessao(sessaoSelecionada, mensagensSessao);
      onClose();
    }
  };

  const handleFinalizarSessao = async (sessaoId: string) => {
    if (confirm('Tem certeza que deseja finalizar esta sessão?')) {
      try {
        await finalizarSessao(sessaoId);
        await carregarSessoes(); // Recarregar lista
      } catch (error) {
        console.error('Erro ao finalizar sessão:', error);
      }
    }
  };

  const formatarData = (dataISO: string) => {
    const data = new Date(dataISO);
    return data.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const obterPreviewMensagem = (titulo: string) => {
    if (titulo && titulo !== 'Nova Conversa') {
      return titulo.length > 50 ? `${titulo.substring(0, 50)}...` : titulo;
    }
    return 'Conversa sem título';
  };

  if (visualizandoDetalhes) {
    const sessao = sessoes.find(s => s.id === sessaoSelecionada);

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm">
        <Card className="w-[90%] max-w-2xl max-h-[90vh] overflow-hidden shadow-2xl animate-in slide-in-from-bottom-4 duration-300 flex flex-col">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setVisualizandoDetalhes(false)}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Voltar
              </Button>
              <CardTitle className="text-lg">Detalhes da Sessão</CardTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            <div className="bg-muted p-4 rounded-lg space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">ID:</span>
                <Badge variant="secondary">{sessao?.id.slice(0, 8)}...</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Status:</span>
                <Badge variant={sessao?.status === 'ativa' ? 'default' : 'secondary'}>
                  {sessao?.status === 'ativa' ? 'Ativa' : 'Finalizada'}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Criada em:</span>
                <span className="text-sm text-muted-foreground">{sessao && formatarData(sessao.criada_em)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Atualizada em:</span>
                <span className="text-sm text-muted-foreground">{sessao && formatarData(sessao.atualizada_em)}</span>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="text-base font-medium">Mensagens ({mensagensSessao.length})</h4>
              <div className="max-h-80 overflow-y-auto border rounded-lg">
                {mensagensSessao.map((msg) => (
                  <div key={msg.id} className={`p-3 border-b last:border-b-0 ${
                    msg.tipo === 'user' ? 'bg-muted/50' : 'bg-background'
                  }`}>
                    <div className="flex justify-between items-center mb-2">
                      <Badge variant={msg.tipo === 'user' ? 'default' : 'secondary'} className="text-xs">
                        {msg.tipo === 'user' ? 'Você' : 'Assistente'}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {formatarData(msg.timestamp)}
                      </span>
                    </div>
                    <div className="text-sm leading-relaxed">
                      {msg.conteudo.length > 100
                        ? `${msg.conteudo.substring(0, 100)}...`
                        : msg.conteudo
                      }
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-center pt-4 border-t">
              <Button
                onClick={carregarSessaoNoChat}
                disabled={!mensagensSessao.length}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Carregar no Chat
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm">
      <Card className="w-[90%] max-w-xl max-h-[90vh] overflow-hidden shadow-2xl animate-in slide-in-from-bottom-4 duration-300 flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Histórico de Conversas</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-y-auto">
          {!recursos.listarSessoes && (
            <div className="text-center py-15 text-muted-foreground">
              <div className="text-4xl mb-4">🔗</div>
              <h4 className="mb-2 font-medium">Histórico não disponível</h4>
              <p className="text-sm">O histórico de sessões não está disponível para conexões via webhook N8N.</p>
              <p className="text-sm mt-2">Use a conexão via API local para acessar o histórico completo.</p>
            </div>
          )}

          {recursos.listarSessoes && carregando && (
            <div className="flex flex-col items-center justify-center py-10 text-muted-foreground">
              <RefreshCw className="h-8 w-8 mb-4 animate-spin" />
              <p>Carregando sessões...</p>
            </div>
          )}

          {recursos.listarSessoes && erro && (
            <div className="bg-destructive/10 text-destructive p-4 rounded-lg text-center border border-destructive/20">
              <p className="mb-3">{erro}</p>
              <Button
                variant="destructive"
                size="sm"
                onClick={carregarSessoes}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Tentar Novamente
              </Button>
            </div>
          )}

          {recursos.listarSessoes && !carregando && !erro && sessoes.length === 0 && (
            <div className="text-center py-15 text-muted-foreground">
              <div className="text-4xl mb-4">💬</div>
              <h4 className="mb-2 font-medium">Nenhuma conversa encontrada</h4>
              <p className="text-sm">Suas conversas com o assistente SOGE aparecerão aqui.</p>
            </div>
          )}

          {recursos.listarSessoes && !carregando && sessoes.length > 0 && (
            <div className="space-y-3">
              {sessoes.map((sessao) => (
                <Card key={sessao.id} className="transition-all duration-200 hover:shadow-md">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium mb-1.5 overflow-hidden text-ellipsis whitespace-nowrap">
                          {obterPreviewMensagem(sessao.titulo)}
                        </div>
                        <div className="flex items-center gap-3 text-sm text-muted-foreground">
                          <Badge variant={sessao.status === 'ativa' ? 'default' : 'secondary'} className="text-xs">
                            {sessao.status === 'ativa' ? 'Ativa' : 'Finalizada'}
                          </Badge>
                          <span className="text-xs">
                            {formatarData(sessao.atualizada_em)}
                          </span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => visualizarDetalhes(sessao.id)}
                          className="h-8 w-8"
                          title="Ver detalhes"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>

                        {sessao.status === 'ativa' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleFinalizarSessao(sessao.id)}
                            className="h-8 w-8 text-destructive hover:text-destructive"
                            title="Finalizar sessão"
                          >
                            <StopCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>

        <div className="p-4 bg-muted/50 border-t flex justify-between items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={carregarSessoes}
            className="gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Atualizar
          </Button>
          <div className="text-sm text-muted-foreground">
            Total: {sessoes.length} sessões
          </div>
        </div>
      </Card>
    </div>
  );
};

export default HistoricoSessoes;
